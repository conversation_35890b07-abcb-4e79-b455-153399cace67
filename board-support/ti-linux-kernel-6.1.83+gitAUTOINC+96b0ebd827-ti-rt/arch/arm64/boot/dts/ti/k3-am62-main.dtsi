// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for AM625 SoC Family Main Domain peripherals
 *
 * Copyright (C) 2020-2022 Texas Instruments Incorporated - https://www.ti.com/
 */

&cbass_main {
	oc_sram: sram@70000000 {
		compatible = "mmio-sram";
		reg = <0x00 0x70000000 0x00 0x10000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x00 0x70000000 0x10000>;
	};

	gic500: interrupt-controller@1800000 {
		compatible = "arm,gic-v3";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x00 0x01800000 0x00 0x10000>,	/* GICD */
		      <0x00 0x01880000 0x00 0xc0000>,	/* GICR */
		      <0x00 0x01880000 0x00 0xc0000>,   /* GICR */
		      <0x01 0x00000000 0x00 0x2000>,    /* GICC */
		      <0x01 0x00010000 0x00 0x1000>,    /* GICH */
		      <0x01 0x00020000 0x00 0x2000>;    /* GICV */
		/*
		 * vcpumntirq:
		 * virtual CPU interface maintenance interrupt
		 */
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;

		gic_its: msi-controller@1820000 {
			compatible = "arm,gic-v3-its";
			reg = <0x00 0x01820000 0x00 0x10000>;
			socionext,synquacer-pre-its = <0x1000000 0x400000>;
			msi-controller;
			#msi-cells = <1>;
		};
	};

	main_conf: syscon@100000 {
		compatible = "syscon", "simple-mfd";
		reg = <0x00 0x00100000 0x00 0x20000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x00 0x00100000 0x20000>;

		phy_gmii_sel: phy@4044 {
			compatible = "ti,am654-phy-gmii-sel";
			reg = <0x4044 0x8>;
			#phy-cells = <1>;
		};

		epwm_tbclk: clock@4130 {
			compatible = "ti,am62-epwm-tbclk";
			reg = <0x4130 0x4>;
			#clock-cells = <1>;
		};

		dss_oldi_io_ctrl: dss-oldi-io-ctrl@8600 {
			compatible = "syscon";
			reg = <0x8600 0x200>;
		};

		audio_refclk0: clock@82e0 {
			compatible = "ti,am62-audio-refclk";
			reg = <0x82e0 0x4>;
			clocks = <&k3_clks 157 0>;
			assigned-clocks = <&k3_clks 157 0>;
			assigned-clock-parents = <&k3_clks 157 8>;
			#clock-cells = <0>;
		};

		audio_refclk1: clock@82e4 {
			compatible = "ti,am62-audio-refclk";
			reg = <0x82e4 0x4>;
			clocks = <&k3_clks 157 10>;
			assigned-clocks = <&k3_clks 157 10>;
			assigned-clock-parents = <&k3_clks 157 18>;
			#clock-cells = <0>;
		};
	};

	dmss: bus@48000000 {
		compatible = "simple-mfd";
		#address-cells = <2>;
		#size-cells = <2>;
		dma-ranges;
		ranges = <0x00 0x48000000 0x00 0x48000000 0x00 0x06400000>;

		ti,sci-dev-id = <25>;

		secure_proxy_main: mailbox@4d000000 {
			compatible = "ti,am654-secure-proxy";
			#mbox-cells = <1>;
			reg-names = "target_data", "rt", "scfg";
			reg = <0x00 0x4d000000 0x00 0x80000>,
			      <0x00 0x4a600000 0x00 0x80000>,
			      <0x00 0x4a400000 0x00 0x80000>;
			interrupt-names = "rx_012";
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
		};

		inta_main_dmss: interrupt-controller@48000000 {
			compatible = "ti,sci-inta";
			reg = <0x00 0x48000000 0x00 0x100000>;
			#interrupt-cells = <0>;
			interrupt-controller;
			interrupt-parent = <&gic500>;
			msi-controller;
			ti,sci = <&dmsc>;
			ti,sci-dev-id = <28>;
			ti,interrupt-ranges = <4 68 36>;
			ti,unmapped-event-sources = <&main_bcdma>, <&main_pktdma>;
		};

		main_bcdma: dma-controller@485c0100 {
			compatible = "ti,am64-dmss-bcdma";
			reg = <0x00 0x485c0100 0x00 0x100>,
			      <0x00 0x4c000000 0x00 0x20000>,
			      <0x00 0x4a820000 0x00 0x20000>,
			      <0x00 0x4aa40000 0x00 0x20000>,
			      <0x00 0x4bc00000 0x00 0x100000>;
			reg-names = "gcfg", "bchanrt", "rchanrt", "tchanrt", "ringrt";
			msi-parent = <&inta_main_dmss>;
			#dma-cells = <3>;

			ti,sci = <&dmsc>;
			ti,sci-dev-id = <26>;
			ti,sci-rm-range-bchan = <0x20>; /* BLOCK_COPY_CHAN */
			ti,sci-rm-range-rchan = <0x21>; /* SPLIT_TR_RX_CHAN */
			ti,sci-rm-range-tchan = <0x22>; /* SPLIT_TR_TX_CHAN */
		};

		main_pktdma: dma-controller@485c0000 {
			compatible = "ti,am64-dmss-pktdma";
			reg = <0x00 0x485c0000 0x00 0x100>,
			      <0x00 0x4a800000 0x00 0x20000>,
			      <0x00 0x4aa00000 0x00 0x40000>,
			      <0x00 0x4b800000 0x00 0x400000>;
			reg-names = "gcfg", "rchanrt", "tchanrt", "ringrt";
			msi-parent = <&inta_main_dmss>;
			#dma-cells = <2>;

			ti,sci = <&dmsc>;
			ti,sci-dev-id = <30>;
			ti,sci-rm-range-tchan = <0x23>, /* UNMAPPED_TX_CHAN */
						<0x24>, /* CPSW_TX_CHAN */
						<0x25>, /* SAUL_TX_0_CHAN */
						<0x26>; /* SAUL_TX_1_CHAN */
			ti,sci-rm-range-tflow = <0x10>, /* RING_UNMAPPED_TX_CHAN */
						<0x11>, /* RING_CPSW_TX_CHAN */
						<0x12>, /* RING_SAUL_TX_0_CHAN */
						<0x13>; /* RING_SAUL_TX_1_CHAN */
			ti,sci-rm-range-rchan = <0x29>, /* UNMAPPED_RX_CHAN */
						<0x2b>, /* CPSW_RX_CHAN */
						<0x2d>, /* SAUL_RX_0_CHAN */
						<0x2f>, /* SAUL_RX_1_CHAN */
						<0x31>, /* SAUL_RX_2_CHAN */
						<0x33>; /* SAUL_RX_3_CHAN */
			ti,sci-rm-range-rflow = <0x2a>, /* FLOW_UNMAPPED_RX_CHAN */
						<0x2c>, /* FLOW_CPSW_RX_CHAN */
						<0x2e>, /* FLOW_SAUL_RX_0/1_CHAN */
						<0x32>; /* FLOW_SAUL_RX_2/3_CHAN */
		};
	};

	dmsc: system-controller@44043000 {
		compatible = "ti,k2g-sci";
		ti,host-id = <12>;
		mbox-names = "rx", "tx";
		mboxes = <&secure_proxy_main 12>,
			 <&secure_proxy_main 13>;
		reg-names = "debug_messages";
		reg = <0x00 0x44043000 0x00 0xfe0>;

		k3_pds: power-controller {
			compatible = "ti,sci-pm-domain";
			#power-domain-cells = <2>;
		};

		k3_clks: clock-controller {
			compatible = "ti,k2g-sci-clk";
			#clock-cells = <2>;
		};

		k3_reset: reset-controller {
			compatible = "ti,sci-reset";
			#reset-cells = <2>;
		};
	};

	crypto: crypto@40900000 {
		compatible = "ti,am62-sa3ul";
		reg = <0x00 0x40900000 0x00 0x1200>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges = <0x00 0x40900000 0x00 0x40900000 0x00 0x30000>;

		dmas = <&main_pktdma 0xf501 0>, <&main_pktdma 0x7506 0>,
		       <&main_pktdma 0x7507 0>;
		dma-names = "tx", "rx1", "rx2";
	};

	mcrc: mcrc@30300000 {
		compatible = "ti,mcrc";
		reg = <0x00 0x30300000 0x00 0x1000>;
		clocks = <&k3_clks 116 0>;
		power-domains = <&k3_pds 116 TI_SCI_PD_EXCLUSIVE>;
	};

	secure_proxy_sa3: mailbox@43600000 {
		compatible = "ti,am654-secure-proxy";
		#mbox-cells = <1>;
		reg-names = "target_data", "rt", "scfg";
		reg = <0x00 0x43600000 0x00 0x10000>,
		      <0x00 0x44880000 0x00 0x20000>,
		      <0x00 0x44860000 0x00 0x20000>;
		/*
		 * Marked Disabled:
		 * Node is incomplete as it is meant for bootloaders and
		 * firmware on non-MPU processors
		 */
		status = "disabled";
	};

	main_pmx0: pinctrl@f4000 {
		compatible = "ti,am6-padconf";
		reg = <0x00 0xf4000 0x00 0x2ac>;
		#pinctrl-cells = <1>;
		pinctrl-single,register-width = <32>;
		pinctrl-single,function-mask = <0xffffffff>;
		interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-controller;
		#interrupt-cells = <1>;
	};

	main_timer0: timer@2400000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2400000 0x00 0x400>;
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 36 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 36 2>;
		assigned-clock-parents = <&k3_clks 36 3>;
		power-domains = <&k3_pds 36 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer1: timer@2410000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2410000 0x00 0x400>;
		interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 37 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 37 2>;
		assigned-clock-parents = <&k3_clks 37 3>;
		power-domains = <&k3_pds 37 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer2: timer@2420000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2420000 0x00 0x400>;
		interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 38 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 38 2>;
		assigned-clock-parents = <&k3_clks 38 3>;
		power-domains = <&k3_pds 38 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer3: timer@2430000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2430000 0x00 0x400>;
		interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 39 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 39 2>;
		assigned-clock-parents = <&k3_clks 39 3>;
		power-domains = <&k3_pds 39 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer4: timer@2440000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2440000 0x00 0x400>;
		interrupts = <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 40 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 40 2>;
		assigned-clock-parents = <&k3_clks 40 3>;
		power-domains = <&k3_pds 40 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer5: timer@2450000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2450000 0x00 0x400>;
		interrupts = <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 41 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 41 2>;
		assigned-clock-parents = <&k3_clks 41 3>;
		power-domains = <&k3_pds 41 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer6: timer@2460000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2460000 0x00 0x400>;
		interrupts = <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 42 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 42 2>;
		assigned-clock-parents = <&k3_clks 42 3>;
		power-domains = <&k3_pds 42 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_timer7: timer@2470000 {
		compatible = "ti,am654-timer";
		reg = <0x00 0x2470000 0x00 0x400>;
		interrupts = <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&k3_clks 43 2>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 43 2>;
		assigned-clock-parents = <&k3_clks 43 3>;
		power-domains = <&k3_pds 43 TI_SCI_PD_EXCLUSIVE>;
		ti,timer-pwm;
	};

	main_esm: esm@420000 {
		compatible = "ti,j721e-esm";
		reg = <0x00 0x420000 0x00 0x1000>;
		ti,esm-pins = <160>, <161>, <162>, <163>, <177>, <178>;
	};

	main_uart0: serial@2800000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02800000 0x00 0x100>;
		interrupts = <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 146 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 146 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart1: serial@2810000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02810000 0x00 0x100>;
		interrupts = <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 152 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 152 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart2: serial@2820000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02820000 0x00 0x100>;
		interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 153 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 153 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart3: serial@2830000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02830000 0x00 0x100>;
		interrupts = <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 154 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 154 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart4: serial@2840000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02840000 0x00 0x100>;
		interrupts = <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 155 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 155 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart5: serial@2850000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02850000 0x00 0x100>;
		interrupts = <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 156 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 156 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_uart6: serial@2860000 {
		compatible = "ti,am64-uart", "ti,am654-uart";
		reg = <0x00 0x02860000 0x00 0x100>;
		interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 158 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 158 0>;
		clock-names = "fclk";
		status = "disabled";
	};

	main_i2c0: i2c@20000000 {
		compatible = "ti,am64-i2c", "ti,omap4-i2c";
		reg = <0x00 0x20000000 0x00 0x100>;
		interrupts = <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 102 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 102 2>;
		clock-names = "fck";
		status = "disabled";
	};

	main_i2c1: i2c@20010000 {
		compatible = "ti,am64-i2c", "ti,omap4-i2c";
		reg = <0x00 0x20010000 0x00 0x100>;
		interrupts = <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 103 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 103 2>;
		clock-names = "fck";
		status = "disabled";
	};

	main_i2c2: i2c@20020000 {
		compatible = "ti,am64-i2c", "ti,omap4-i2c";
		reg = <0x00 0x20020000 0x00 0x100>;
		interrupts = <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 104 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 104 2>;
		clock-names = "fck";
		status = "disabled";
	};

	main_i2c3: i2c@20030000 {
		compatible = "ti,am64-i2c", "ti,omap4-i2c";
		reg = <0x00 0x20030000 0x00 0x100>;
		interrupts = <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 105 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 105 2>;
		clock-names = "fck";
		status = "disabled";
	};

	main_spi0: spi@20100000 {
		compatible = "ti,am654-mcspi", "ti,omap4-mcspi";
		reg = <0x00 0x20100000 0x00 0x400>;
		interrupts = <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 141 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 141 0>;
		status = "disabled";
	};

	main_spi1: spi@20110000 {
		compatible = "ti,am654-mcspi","ti,omap4-mcspi";
		reg = <0x00 0x20110000 0x00 0x400>;
		interrupts = <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 142 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 142 0>;
		status = "disabled";
	};

	main_spi2: spi@20120000 {
		compatible = "ti,am654-mcspi","ti,omap4-mcspi";
		reg = <0x00 0x20120000 0x00 0x400>;
		interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		power-domains = <&k3_pds 143 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 143 0>;
		status = "disabled";
	};

	main_gpio_intr: interrupt-controller@a00000 {
		compatible = "ti,sci-intr";
		reg = <0x00 0x00a00000 0x00 0x800>;
		ti,intr-trigger-type = <1>;
		interrupt-controller;
		interrupt-parent = <&gic500>;
		#interrupt-cells = <1>;
		ti,sci = <&dmsc>;
		ti,sci-dev-id = <3>;
		ti,interrupt-ranges = <0 32 16>;
	};

	main_gpio0: gpio@600000 {
		compatible = "ti,am64-gpio", "ti,keystone-gpio";
		reg = <0x0 0x00600000 0x0 0x100>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&main_gpio_intr>;
		interrupts = <190>, <191>, <192>,
			     <193>, <194>, <195>;
		interrupt-controller;
		#interrupt-cells = <2>;
		ti,ngpio = <92>;
		ti,davinci-gpio-unbanked = <0>;
		power-domains = <&k3_pds 77 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 77 0>;
		clock-names = "gpio";
	};

	main_gpio1: gpio@601000 {
		compatible = "ti,am64-gpio", "ti,keystone-gpio";
		reg = <0x0 0x00601000 0x0 0x100>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&main_gpio_intr>;
		interrupts = <180>, <181>, <182>,
			     <183>, <184>, <185>;
		interrupt-controller;
		#interrupt-cells = <2>;
		ti,ngpio = <52>;
		ti,davinci-gpio-unbanked = <0>;
		power-domains = <&k3_pds 78 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 78 0>;
		clock-names = "gpio";
	};

	sdhci0: mmc@fa10000 {
		compatible = "ti,am62-sdhci";
		reg = <0x00 0x0fa10000 0x00 0x1000>, <0x00 0x0fa18000 0x00 0x400>;
		interrupts = <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 57 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 57 5>, <&k3_clks 57 6>;
		clock-names = "clk_ahb", "clk_xin";
		assigned-clocks = <&k3_clks 57 6>;
		assigned-clock-parents = <&k3_clks 57 8>;
		bus-width = <8>;
		mmc-ddr-1_8v;
		mmc-hs200-1_8v;
		ti,clkbuf-sel = <0x7>;
		ti,otap-del-sel-legacy = <0x0>;
		ti,otap-del-sel-mmc-hs = <0x0>;
		ti,otap-del-sel-ddr52 = <0x5>;
		ti,otap-del-sel-hs200 = <0x5>;
		ti,itap-del-sel-legacy = <0xa>;
		ti,itap-del-sel-mmc-hs = <0x1>;
		status = "disabled";
	};

	sdhci1: mmc@fa00000 {
		compatible = "ti,am62-sdhci";
		reg = <0x00 0x0fa00000 0x00 0x1000>, <0x00 0x0fa08000 0x00 0x400>;
		interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 58 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 58 5>, <&k3_clks 58 6>;
		clock-names = "clk_ahb", "clk_xin";
		bus-width = <4>;
		ti,clkbuf-sel = <0x7>;
		ti,otap-del-sel-legacy = <0x8>;
		ti,otap-del-sel-sd-hs = <0x0>;
		ti,otap-del-sel-sdr12 = <0x0>;
		ti,otap-del-sel-sdr25 = <0x0>;
		ti,otap-del-sel-sdr50 = <0x8>;
		ti,otap-del-sel-sdr104 = <0x7>;
		ti,otap-del-sel-ddr50 = <0x4>;
		ti,itap-del-sel-legacy = <0xa>;
		ti,itap-del-sel-sd-hs = <0x1>;
		ti,itap-del-sel-sdr12 = <0xa>;
		ti,itap-del-sel-sdr25 = <0x1>;
		status = "disabled";
	};

	sdhci2: mmc@fa20000 {
		compatible = "ti,am62-sdhci";
		reg = <0x00 0x0fa20000 0x00 0x1000>, <0x00 0x0fa28000 0x00 0x400>;
		interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 184 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 184 5>, <&k3_clks 184 6>;
		clock-names = "clk_ahb", "clk_xin";
		bus-width = <4>;
		ti,clkbuf-sel = <0x7>;
		ti,otap-del-sel-legacy = <0x8>;
		ti,otap-del-sel-sd-hs = <0x0>;
		ti,otap-del-sel-sdr12 = <0x0>;
		ti,otap-del-sel-sdr25 = <0x0>;
		ti,otap-del-sel-sdr50 = <0x8>;
		ti,otap-del-sel-sdr104 = <0x7>;
		ti,otap-del-sel-ddr50 = <0x8>;
		ti,itap-del-sel-legacy = <0xa>;
		ti,itap-del-sel-sd-hs = <0xa>;
		ti,itap-del-sel-sdr12 = <0xa>;
		ti,itap-del-sel-sdr25 = <0x1>;
		status = "disabled";
	};

	gpu: gpu@fd00000 {
/*		compatible = "ti,am62-pvr", "img,pvr-axe116m";
		reg = <0x00 0x0fd00000 0x00 0x20000>;
		interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 187 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 187 0>; */
		status = "disabled";
	};

	usbss0: dwc3-usb@f900000 {
		compatible = "ti,am62-usb";
		reg = <0x00 0x0f900000 0x00 0x800>,
		      <0x00 0x0f908000 0x00 0x400>;
		clocks = <&k3_clks 161 3>;
		clock-names = "ref";
		ti,syscon-phy-pll-refclk = <&usb0_phy_ctrl 0x0>;
		#address-cells = <2>;
		#size-cells = <2>;
		power-domains = <&k3_pds 178 TI_SCI_PD_EXCLUSIVE>;
		ranges;
		status = "disabled";

		usb0: usb@31000000 {
			compatible = "snps,dwc3";
			reg =<0x00 0x31000000 0x00 0x50000>;
			interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>, /* irq.0 */
				     <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>; /* irq.0 */
			interrupt-names = "host", "peripheral";
			maximum-speed = "high-speed";
			dr_mode = "otg";
		};
	};

	usbss1: dwc3-usb@f910000 {
		compatible = "ti,am62-usb";
		reg = <0x00 0x0f910000 0x00 0x800>,
		      <0x00 0x0f918000 0x00 0x400>;
		clocks = <&k3_clks 162 3>;
		clock-names = "ref";
		ti,syscon-phy-pll-refclk = <&usb1_phy_ctrl 0x0>;
		#address-cells = <2>;
		#size-cells = <2>;
		power-domains = <&k3_pds 179 TI_SCI_PD_EXCLUSIVE>;
		ranges;
		status = "disabled";

		usb1: usb@31100000 {
			compatible = "snps,dwc3";
			reg =<0x00 0x31100000 0x00 0x50000>;
			interrupts = <GIC_SPI 226 IRQ_TYPE_LEVEL_HIGH>, /* irq.0 */
				     <GIC_SPI 226 IRQ_TYPE_LEVEL_HIGH>; /* irq.0 */
			interrupt-names = "host", "peripheral";
			maximum-speed = "high-speed";
			dr_mode = "otg";
		};
	};

	fss: bus@fc00000 {
		compatible = "simple-pm-bus";
		reg = <0x00 0x0fc00000 0x00 0x70000>;
		power-domains = <&k3_pds 74 TI_SCI_PD_EXCLUSIVE>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		ospi0: spi@fc40000 {
			compatible = "ti,am654-ospi", "cdns,qspi-nor";
			reg = <0x00 0x0fc40000 0x00 0x100>,
			      <0x05 0x00000000 0x01 0x00000000>;
			interrupts = <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>;
			cdns,fifo-depth = <256>;
			cdns,fifo-width = <4>;
			cdns,trigger-address = <0x0>;
			clocks = <&k3_clks 75 7>;
			assigned-clocks = <&k3_clks 75 7>;
			assigned-clock-parents = <&k3_clks 75 8>;
			assigned-clock-rates = <166666666>;
			power-domains = <&k3_pds 75 TI_SCI_PD_EXCLUSIVE>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};
	};

	cpsw3g: ethernet@8000000 {
		compatible = "ti,am642-cpsw-nuss";
		#address-cells = <2>;
		#size-cells = <2>;
		reg = <0x00 0x08000000 0x00 0x200000>;
		reg-names = "cpsw_nuss";
		ranges = <0x00 0x00 0x00 0x08000000 0x00 0x200000>;
		clocks = <&k3_clks 13 0>;
		assigned-clocks = <&k3_clks 13 3>;
		assigned-clock-parents = <&k3_clks 13 11>;
		clock-names = "fck";
		power-domains = <&k3_pds 13 TI_SCI_PD_EXCLUSIVE>;

		dmas = <&main_pktdma 0xc600 15>,
		       <&main_pktdma 0xc601 15>,
		       <&main_pktdma 0xc602 15>,
		       <&main_pktdma 0xc603 15>,
		       <&main_pktdma 0xc604 15>,
		       <&main_pktdma 0xc605 15>,
		       <&main_pktdma 0xc606 15>,
		       <&main_pktdma 0xc607 15>,
		       <&main_pktdma 0x4600 15>;
		dma-names = "tx0", "tx1", "tx2", "tx3", "tx4", "tx5", "tx6",
			    "tx7", "rx";

		ethernet-ports {
			#address-cells = <1>;
			#size-cells = <0>;

			cpsw_port1: port@1 {
				reg = <1>;
				ti,mac-only;
				label = "port1";
				phys = <&phy_gmii_sel 1>;
				mac-address = [00 00 00 00 00 00];
				ti,syscon-efuse = <&wkup_conf 0x200>;
			};

			cpsw_port2: port@2 {
				reg = <2>;
				ti,mac-only;
				label = "port2";
				phys = <&phy_gmii_sel 2>;
				mac-address = [00 00 00 00 00 00];
			};
		};

		cpsw3g_mdio: mdio@f00 {
			compatible = "ti,cpsw-mdio","ti,davinci_mdio";
			reg = <0x00 0xf00 0x00 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&k3_clks 13 0>;
			clock-names = "fck";
			bus_freq = <1000000>;
			status = "disabled";
		};

		cpts@3d000 {
			compatible = "ti,j721e-cpts";
			reg = <0x00 0x3d000 0x00 0x400>;
			clocks = <&k3_clks 13 3>;
			clock-names = "cpts";
			interrupts-extended = <&gic500 GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "cpts";
			ti,cpts-ext-ts-inputs = <4>;
			ti,cpts-periodic-outputs = <2>;
		};
	};

	dss: dss@30200000 {
		compatible = "ti,am625-dss";
		reg = <0x00 0x30200000 0x00 0x1000>, /* common */
		      <0x00 0x30202000 0x00 0x1000>, /* vidl1 */
		      <0x00 0x30206000 0x00 0x1000>, /* vid */
		      <0x00 0x30207000 0x00 0x1000>, /* ovr1 */
		      <0x00 0x30208000 0x00 0x1000>, /* ovr2 */
		      <0x00 0x3020a000 0x00 0x1000>, /* vp1: Used for OLDI */
		      <0x00 0x3020b000 0x00 0x1000>, /* vp2: Used as DPI Out */
		      <0x00 0x30201000 0x00 0x1000>; /* common1 */
		reg-names = "common", "vidl1", "vid",
			    "ovr1", "ovr2", "vp1", "vp2", "common1";
		ti,am65x-oldi-io-ctrl = <&dss_oldi_io_ctrl>;
		power-domains = <&k3_pds 186 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 186 6>,
			 <&k3_clks 186 0>,
			 <&k3_clks 186 2>;
		clock-names = "fck", "vp1", "vp2";
		interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;

		dss_ports: ports {
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};

	timesync_router: pinctrl@a40000 {
		compatible = "pinctrl-single";
		reg = <0x0 0xa40000 0x0 0x800>;
		#pinctrl-cells = <1>;
		pinctrl-single,register-width = <32>;
		pinctrl-single,function-mask = <0x000107ff>;
		status = "disabled";
	};

	hwspinlock: spinlock@2a000000 {
		compatible = "ti,am64-hwspinlock";
		reg = <0x00 0x2a000000 0x00 0x1000>;
		#hwlock-cells = <1>;
	};

	mailbox0_cluster0: mailbox@29000000 {
		compatible = "ti,am64-mailbox";
		reg = <0x00 0x29000000 0x00 0x200>;
		interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
		#mbox-cells = <1>;
		ti,mbox-num-users = <4>;
		ti,mbox-num-fifos = <16>;
	};

	ecap0: pwm@23100000 {
		compatible = "ti,am3352-ecap";
		#pwm-cells = <3>;
		reg = <0x00 0x23100000 0x00 0x100>;
		power-domains = <&k3_pds 51 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 51 0>;
		clock-names = "fck";
		status = "disabled";
	};

	ecap1: pwm@23110000 {
		compatible = "ti,am3352-ecap";
		#pwm-cells = <3>;
		reg = <0x00 0x23110000 0x00 0x100>;
		power-domains = <&k3_pds 52 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 52 0>;
		clock-names = "fck";
		status = "disabled";
	};

	ecap2: pwm@23120000 {
		compatible = "ti,am3352-ecap";
		#pwm-cells = <3>;
		reg = <0x00 0x23120000 0x00 0x100>;
		power-domains = <&k3_pds 53 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 53 0>;
		clock-names = "fck";
		status = "disabled";
	};

	eqep0: counter@23200000 {
		compatible = "ti,am3352-eqep";
		reg = <0x00 0x23200000 0x00 0x100>;
		power-domains = <&k3_pds 59 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 59 0>;
		clock-names = "fck";
		interrupts = <GIC_SPI 116 IRQ_TYPE_EDGE_RISING>;
		status = "disabled";
	};

	eqep1: counter@23210000 {
		compatible = "ti,am3352-eqep";
		reg = <0x00 0x23210000 0x00 0x100>;
		power-domains = <&k3_pds 60 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 60 0>;
		clock-names = "fck";
		interrupts = <GIC_SPI 117 IRQ_TYPE_EDGE_RISING>;
		status = "disabled";
	};

	eqep2: counter@23220000 {
		compatible = "ti,am3352-eqep";
		reg = <0x00 0x23220000 0x00 0x100>;
		power-domains = <&k3_pds 62 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 62 0>;
		clock-names = "fck";
		interrupts = <GIC_SPI 118 IRQ_TYPE_EDGE_RISING>;
		status = "disabled";
	};

	main_mcan0: can@20701000 {
		compatible = "bosch,m_can";
		reg = <0x00 0x20701000 0x00 0x200>,
		      <0x00 0x20708000 0x00 0x8000>;
		reg-names = "m_can", "message_ram";
		power-domains = <&k3_pds 98 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 98 6>, <&k3_clks 98 1>;
		clock-names = "hclk", "cclk";
		interrupts = <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "int0", "int1";
		bosch,mram-cfg = <0x0 128 64 64 64 64 32 32>;
		status = "disabled";
	};

	main_rti0: watchdog@e000000 {
		compatible = "ti,j7-rti-wdt";
		reg = <0x00 0x0e000000 0x00 0x100>;
		clocks = <&k3_clks 125 0>;
		power-domains = <&k3_pds 125 TI_SCI_PD_EXCLUSIVE>;
		assigned-clocks = <&k3_clks 125 0>;
		assigned-clock-parents = <&k3_clks 125 2>;
		status = "okay";
	};

	main_rti1: watchdog@e010000 {
/*		compatible = "ti,j7-rti-wdt";
		reg = <0x00 0x0e010000 0x00 0x100>;
		clocks = <&k3_clks 126 0>;
		power-domains = <&k3_pds 126 TI_SCI_PD_EXCLUSIVE>;
		assigned-clocks = <&k3_clks 126 0>;
		assigned-clock-parents = <&k3_clks 126 2>; */
		status = "disabled";
	};

	main_rti2: watchdog@e020000 {
/*		compatible = "ti,j7-rti-wdt";
		reg = <0x00 0x0e020000 0x00 0x100>;
		clocks = <&k3_clks 127 0>;
		power-domains = <&k3_pds 127 TI_SCI_PD_EXCLUSIVE>;
		assigned-clocks = <&k3_clks 127 0>;
		assigned-clock-parents = <&k3_clks 127 2>; */
		status = "disabled";
	};

	main_rti3: watchdog@e030000 {
/*		compatible = "ti,j7-rti-wdt";
		reg = <0x00 0x0e030000 0x00 0x100>;
		clocks = <&k3_clks 128 0>;
		power-domains = <&k3_pds 128 TI_SCI_PD_EXCLUSIVE>;
		assigned-clocks = <&k3_clks 128 0>;
		assigned-clock-parents = <&k3_clks 128 2>; */
		status = "disabled";
	};

	main_rti15: watchdog@e0f0000 {
/*		compatible = "ti,j7-rti-wdt";
		reg = <0x00 0x0e0f0000 0x00 0x100>;
		clocks = <&k3_clks 130 0>;
		power-domains = <&k3_pds 130 TI_SCI_PD_EXCLUSIVE>;
		assigned-clocks = <&k3_clks 130 0>;
		assigned-clock-parents = <&k3_clks 130 2>; */
		status = "disabled";
	};

	epwm0: pwm@23000000 {
		compatible = "ti,am64-epwm", "ti,am3352-ehrpwm";
		#pwm-cells = <3>;
		reg = <0x00 0x23000000 0x00 0x100>;
		power-domains = <&k3_pds 86 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&epwm_tbclk 0>, <&k3_clks 86 0>;
		clock-names = "tbclk", "fck";
		status = "disabled";
	};

	epwm1: pwm@23010000 {
		compatible = "ti,am64-epwm", "ti,am3352-ehrpwm";
		#pwm-cells = <3>;
		reg = <0x00 0x23010000 0x00 0x100>;
		power-domains = <&k3_pds 87 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&epwm_tbclk 1>, <&k3_clks 87 0>;
		clock-names = "tbclk", "fck";
		status = "disabled";
	};

	epwm2: pwm@23020000 {
		compatible = "ti,am64-epwm", "ti,am3352-ehrpwm";
		#pwm-cells = <3>;
		reg = <0x00 0x23020000 0x00 0x100>;
		power-domains = <&k3_pds 88 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&epwm_tbclk 2>, <&k3_clks 88 0>;
		clock-names = "tbclk", "fck";
		status = "disabled";
	};

	mcasp0: audio-controller@2b00000 {
		compatible = "ti,am33xx-mcasp-audio";
		reg = <0x00 0x02b00000 0x00 0x2000>,
		      <0x00 0x02b08000 0x00 0x400>;
		reg-names = "mpu", "dat";
		interrupts = <GIC_SPI 236 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 235 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "tx", "rx";

		dmas = <&main_bcdma 0 0xc500 0>, <&main_bcdma 0 0x4500 0>;
		dma-names = "tx", "rx";

		clocks = <&k3_clks 190 0>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 190 0>;
		assigned-clock-parents = <&k3_clks 190 2>;
		power-domains = <&k3_pds 190 TI_SCI_PD_EXCLUSIVE>;
		status = "disabled";
	};

	mcasp1: audio-controller@2b10000 {
		compatible = "ti,am33xx-mcasp-audio";
		reg = <0x00 0x02b10000 0x00 0x2000>,
		      <0x00 0x02b18000 0x00 0x400>;
		reg-names = "mpu", "dat";
		interrupts = <GIC_SPI 238 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 237 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "tx", "rx";

		dmas = <&main_bcdma 0 0xc501 0>, <&main_bcdma 0 0x4501 0>;
		dma-names = "tx", "rx";

		clocks = <&k3_clks 191 0>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 191 0>;
		assigned-clock-parents = <&k3_clks 191 2>;
		power-domains = <&k3_pds 191 TI_SCI_PD_EXCLUSIVE>;
		status = "disabled";
	};

	mcasp2: audio-controller@2b20000 {
		compatible = "ti,am33xx-mcasp-audio";
		reg = <0x00 0x02b20000 0x00 0x2000>,
		      <0x00 0x02b28000 0x00 0x400>;
		reg-names = "mpu", "dat";
		interrupts = <GIC_SPI 240 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 239 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "tx", "rx";

		dmas = <&main_bcdma 0 0xc502 0>, <&main_bcdma 0 0x4502 0>;
		dma-names = "tx", "rx";

		clocks = <&k3_clks 192 0>;
		clock-names = "fck";
		assigned-clocks = <&k3_clks 192 0>;
		assigned-clock-parents = <&k3_clks 192 2>;
		power-domains = <&k3_pds 192 TI_SCI_PD_EXCLUSIVE>;
		status = "disabled";
	};

	ti_csi2rx0: ticsi2rx@30102000 {
		compatible = "ti,j721e-csi2rx";
		dmas = <&main_bcdma 0 0x4700 0>, <&main_bcdma 0 0x4701 0>, <&main_bcdma 0 0x4702 0>,
			<&main_bcdma 0 0x4703 0>;
		dma-names = "rx0", "rx1", "rx2", "rx3";
		reg = <0x00 0x30102000 0x00 0x1000>;
		power-domains = <&k3_pds 182 TI_SCI_PD_EXCLUSIVE>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		status = "disabled";

		cdns_csi2rx0: csi-bridge@30101000 {
			compatible = "cdns,csi2rx";
			reg = <0x00 0x30101000 0x00 0x1000>;
			clocks = <&k3_clks 182 0>, <&k3_clks 182 3>, <&k3_clks 182 0>,
				<&k3_clks 182 0>, <&k3_clks 182 4>, <&k3_clks 182 4>;
			clock-names = "sys_clk", "p_clk", "pixel_if0_clk",
				"pixel_if1_clk", "pixel_if2_clk", "pixel_if3_clk";
			phys = <&dphy0>;
			phy-names = "dphy";
			power-domains = <&k3_pds 182 TI_SCI_PD_EXCLUSIVE>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				csi0_port0: port@0 {
					reg = <0>;
					status = "disabled";
				};

				csi0_port1: port@1 {
					reg = <1>;
					status = "disabled";
				};

				csi0_port2: port@2 {
					reg = <2>;
					status = "disabled";
				};

				csi0_port3: port@3 {
					reg = <3>;
					status = "disabled";
				};

				csi0_port4: port@4 {
					reg = <4>;
					status = "disabled";
				};
			};
		};
	};

	dphy0: phy@30110000 {
		compatible = "cdns,dphy-rx";
		reg = <0x00 0x30110000 0x00 0x1100>;
		#phy-cells = <0>;
		power-domains = <&k3_pds 185 TI_SCI_PD_EXCLUSIVE>;
		status = "disabled";
	};

	gpmc0: memory-controller@3b000000 {
		status = "disabled";
		compatible = "ti,am64-gpmc";
		power-domains = <&k3_pds 80 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 80 0>;
		clock-names = "fck";
		reg = <0x00 0x03b000000 0x00 0x400>,
		      <0x00 0x050000000 0x00 0x8000000>;
		reg-names = "cfg", "data";
		interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
		gpmc,num-cs = <3>;
		gpmc,num-waitpins = <2>;
		#address-cells = <2>;
		#size-cells = <1>;
		interrupt-controller;
		#interrupt-cells = <2>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	elm0: ecc@25010000 {
		status = "disabled";
		compatible = "ti,am3352-elm";
		reg = <0x00 0x25010000 0x00 0x2000>;
		interrupts = <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 54 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 54 0>;
		clock-names = "fck";
	};

	ecap2: pwm@23120000 {
		compatible = "ti,am3352-ecap";
		#pwm-cells = <3>;
		reg = <0x00 0x23120000 0x00 0x100>;
		power-domains = <&k3_pds 53 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 53 0>;
		clock-names = "fck";
	};

	pruss: pruss@30040000 {
		compatible = "ti,am625-pruss";
		reg = <0x00 0x30040000 0x00 0x80000>;
		power-domains = <&k3_pds 81 TI_SCI_PD_EXCLUSIVE>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x00 0x30040000 0x80000>;

		pruss_mem: memories@0 {
			reg = <0x0 0x2000>,
			      <0x2000 0x2000>,
			      <0x10000 0x10000>;
			reg-names = "dram0", "dram1", "shrdram2";
		};

		pruss_cfg: cfg@26000 {
			compatible = "ti,pruss-cfg", "syscon";
			reg = <0x26000 0x200>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x26000 0x2000>;

			clocks {
				#address-cells = <1>;
				#size-cells = <0>;

				pruss_coreclk_mux: coreclk-mux@3c {
					reg = <0x3c>;
					#clock-cells = <0>;
					clocks = <&k3_clks 81 0>,  /* pruss_core_clk */
						 <&k3_clks 81 20>; /* pruss_iclk */
					assigned-clocks = <&pruss_coreclk_mux>;
					assigned-clock-parents = <&k3_clks 81 20>;
				};

				pruss_iepclk_mux: iepclk-mux@30 {
					reg = <0x30>;
					#clock-cells = <0>;
					clocks = <&k3_clks 81 3>,	/* pruss_iep_clk */
						 <&pruss_coreclk_mux>;	/* pruss_coreclk_mux */
					assigned-clocks = <&pruss_iepclk_mux>;
					assigned-clock-parents = <&pruss_coreclk_mux>;
				};
			};
		};

		pruss_intc: interrupt-controller@20000 {
			compatible = "ti,pruss-intc";
			reg = <0x20000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "host_intr0", "host_intr1",
					  "host_intr2", "host_intr3",
					  "host_intr4", "host_intr5",
					  "host_intr6", "host_intr7";
		};

		pru0: pru@34000 {
			compatible = "ti,am625-pru";
			reg = <0x34000 0x3000>,
			      <0x22000 0x100>,
			      <0x22400 0x100>;
			reg-names = "iram", "control", "debug";
			firmware-name = "am62x-pru0-fw";
			interrupt-parent = <&pruss_intc>;
			interrupts = <16 2 2>;
			interrupt-names = "vring";
		};

		pru1: pru@38000 {
			compatible = "ti,am625-pru";
			reg = <0x38000 0x3000>,
			      <0x24000 0x100>,
			      <0x24400 0x100>;
			reg-names = "iram", "control", "debug";
			firmware-name = "am62x-pru1-fw";
			interrupt-parent = <&pruss_intc>;
			interrupts = <18 3 3>;
			interrupt-names = "vring";
		};
	};
};
