// SPDX-License-Identifier: GPL-2.0
/*
 * AM625: SoC specific initialization
 *
 * Copyright (C) 2020-2022 Texas Instruments Incorporated - https://www.ti.com/
 *	<PERSON><PERSON> <<EMAIL>>
 */

#include <spl.h>
#include <asm/io.h>
#include <asm/arch/hardware.h>
#include <asm/arch/sysfw-loader.h>
#include "common.h"
#include <dm.h>
#include <k3_gpio.h>
#include <dm/uclass-internal.h>
#include <dm/pinctrl.h>
#include <linux/delay.h>
#include <time.h>

#define RTC_BASE_ADDRESS		0x2b1f0000
#define REG_K3RTC_S_CNT_LSW		(RTC_BASE_ADDRESS + 0x18)
#define REG_K3RTC_KICK0			(RTC_BASE_ADDRESS + 0x70)
#define REG_K3RTC_KICK1			(RTC_BASE_ADDRESS + 0x74)

/* Magic values for lock/unlock */
#define K3RTC_KICK0_UNLOCK_VALUE	0x83e70b13
#define K3RTC_KICK1_UNLOCK_VALUE	0x95a4f1e0

/* WKUP_CTRL_MMR_RST_SRC */
#define WKUP_CTRL_MMR0_CFG0_BASE    0x43000000UL
#define CSL_WKUP_CTRL_MMR_CFG0_RST_SRC  0x00018178UL
#define RESET_SRC_PHYSICAL_ADDR     (WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_RST_SRC)

/* MCU_CTRL_MMR_RST_SRC */
#define MCU_CTRL_MMR0_CFG0_BASE     0x04500000
#define CSL_MCU_CTRL_MMR_CFG0_RST_SRC   0x00018178
#define MCU_CTRL_RST_SRC_PHYSICAL_ADDR (MCU_CTRL_MMR0_CFG0_BASE + CSL_MCU_CTRL_MMR_CFG0_RST_SRC)

/*
 * This uninitialized global variable would normal end up in the .bss section,
 * but the .bss is cleared between writing and reading this variable, so move
 * it to the .data section.
 */
u32 bootindex __section(".data");
static struct rom_extended_boot_data bootdata __section(".data");

static void store_boot_info_from_rom(void)
{
	bootindex = *(u32 *)(CONFIG_SYS_K3_BOOT_PARAM_TABLE_INDEX);
	memcpy(&bootdata, (uintptr_t *)ROM_EXTENDED_BOOT_DATA_INFO,
	       sizeof(struct rom_extended_boot_data));
}

static inline void trigger_power_off(void)
{
    am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 0);
    mdelay(100);
    am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 1);
}

static int check_power_button_spl(void)
{
    int button_state, prev_state;
    int press_count = 0;
    const int CHECK_INTERVAL_MS = 50;        // 检查间隔50ms
    const int LONG_PRESS_COUNT = 10;         // 500ms长按 (10 * 50ms)
    
    /* 读取初始状态 */
    prev_state = am62x_gpio_get_value(AM62X_GPIO0_BASE, POWER_BUTTON_GPIO);
    
    /* 如果按钮未按下，直接关机 */
    if (prev_state != 0) {
        printf("Power button not pressed, triggering power off\n");
        trigger_power_off();
        return 0;
    }
    
    printf("Power button pressed, checking duration...\n");
    
    /* 时间片轮询检测 */
    while (press_count < LONG_PRESS_COUNT) {
        mdelay(CHECK_INTERVAL_MS);
        button_state = am62x_gpio_get_value(AM62X_GPIO0_BASE, POWER_BUTTON_GPIO);
        
        /* 按钮释放检测 */
        if (button_state != 0) {
			printf("Short press detected, triggering power off\n");
			trigger_power_off();
        } 
        
        press_count++;
    }
    
    printf("Long press detected, entering U-Boot interactive mode\n");
    return 0;
}

static void ctrl_mmr_unlock(void)
{
	/* Unlock all WKUP_CTRL_MMR0 module registers */
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 0);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 1);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 2);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 3);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 4);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 5);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 6);
	mmr_unlock(WKUP_CTRL_MMR0_BASE, 7);

	/* Unlock all CTRL_MMR0 module registers */
	mmr_unlock(CTRL_MMR0_BASE, 0);
	mmr_unlock(CTRL_MMR0_BASE, 1);
	mmr_unlock(CTRL_MMR0_BASE, 2);
	mmr_unlock(CTRL_MMR0_BASE, 4);
	mmr_unlock(CTRL_MMR0_BASE, 6);

	/* Unlock all MCU_CTRL_MMR0 module registers */
	mmr_unlock(MCU_CTRL_MMR0_BASE, 0);
	mmr_unlock(MCU_CTRL_MMR0_BASE, 1);
	mmr_unlock(MCU_CTRL_MMR0_BASE, 2);
	mmr_unlock(MCU_CTRL_MMR0_BASE, 3);
	mmr_unlock(MCU_CTRL_MMR0_BASE, 4);
	mmr_unlock(MCU_CTRL_MMR0_BASE, 6);

	/* Unlock PADCFG_CTRL_MMR padconf registers */
	mmr_unlock(PADCFG_MMR0_BASE, 1);
	mmr_unlock(PADCFG_MMR1_BASE, 1);
}

static __maybe_unused void enable_mcu_esm_reset(void)
{
	/* Set CTRLMMR_MCU_RST_CTRL:MCU_ESM_ERROR_RST_EN_Z  to '0' (low active) */
	u32 stat = readl(CTRLMMR_MCU_RST_CTRL);

	stat &= RST_CTRL_ESM_ERROR_RST_EN_Z_MASK;
	writel(stat, CTRLMMR_MCU_RST_CTRL);
}

#if defined(CONFIG_CPU_V7R)

/*
 * RTC Erratum i2327 Workaround
 * Due to a bug in initial synchronization out of cold power on,
 * IRQ status can get locked infinitely if we do not:
 * a) unlock RTC
 *
 * This workaround *must* be applied within 1 second of power on,
 * So, this is closest point to be able to guarantee the max
 * timing.
 */
void rtc_erratumi2327_init(void)
{
	u32 counter;

	/*
	 * If counter has gone past 1, nothing we can do, leave
	 * system locked! This is the only way we know if RTC
	 * can be used for all practical purposes.
	 */
	counter = readl(REG_K3RTC_S_CNT_LSW);
	if (counter > 1)
		return;
	/*
	 * Need to set this up at the very start
	 * MUST BE DONE under 1 second of boot.
	 */
	writel(K3RTC_KICK0_UNLOCK_VALUE, REG_K3RTC_KICK0);
	writel(K3RTC_KICK1_UNLOCK_VALUE, REG_K3RTC_KICK1);
	return;
}
#endif

void board_init_f(ulong dummy)
{
	struct udevice *dev;
	int ret;

#if defined(CONFIG_CPU_V7R)
	setup_k3_mpu_regions();
	rtc_erratumi2327_init();
#endif

	/*
	 * Cannot delay this further as there is a chance that
	 * K3_BOOT_PARAM_TABLE_INDEX can be over written by SPL MALLOC section.
	 */
	store_boot_info_from_rom();

	ctrl_mmr_unlock();

	/* Init DM early */
	spl_early_init();

	wkup_ctrl_remove_can_io_isolation_if_set();

	/*
	 * Process pinctrl for the serial0 and serial3, aka WKUP_UART0 and
	 * MAIN_UART1 modules and continue regardless of the result of pinctrl.
	 * Do this without probing the device, but instead by searching the
	 * device that would request the given sequence number if probed. The
	 * UARTs will be used by the DM firmware and TIFS firmware images
	 * respectively and the firmware depend on SPL to initialize the pin
	 * settings.
	 */
	ret = uclass_find_device_by_seq(UCLASS_SERIAL, 0, &dev);
	if (!ret)
		pinctrl_select_state(dev, "default");

	ret = uclass_find_device_by_seq(UCLASS_SERIAL, 3, &dev);
	if (!ret)
		pinctrl_select_state(dev, "default");

	preloader_console_init();

#ifdef CONFIG_K3_EARLY_CONS
	/*
	 * Allow establishing an early console as required for example when
	 * doing a UART-based boot. Note that this console may not "survive"
	 * through a SYSFW PM-init step and will need a re-init in some way
	 * due to changing module clock frequencies.
	 */
	early_console_init();
#endif

#if defined(CONFIG_K3_LOAD_SYSFW)
	/*
	 * Configure and start up system controller firmware. Provide
	 * the U-Boot console init function to the SYSFW post-PM configuration
	 * callback hook, effectively switching on (or over) the console
	 * output.
	 */
	ret = is_rom_loaded_sysfw(&bootdata);
	if (!ret)
		panic("ROM has not loaded TIFS firmware\n");

	k3_sysfw_loader(true, NULL, NULL);
#endif

	/*
	 * Force probe of clk_k3 driver here to ensure basic default clock
	 * configuration is always done.
	 */
	if (IS_ENABLED(CONFIG_SPL_CLK_K3)) {
		ret = uclass_get_device_by_driver(UCLASS_CLK,
						  DM_DRIVER_GET(ti_clk),
						  &dev);
		if (ret)
			printf("Failed to initialize clk-k3!\n");
	}

	/* Output System Firmware version info */
	k3_sysfw_print_ver();

	if (IS_ENABLED(CONFIG_ESM_K3)) {
		/* Probe/configure ESM0 */
		ret = uclass_get_device_by_name(UCLASS_MISC, "esm@420000", &dev);
		if (ret)
			printf("esm main init failed: %d\n", ret);

		/* Probe/configure MCUESM */
		ret = uclass_get_device_by_name(UCLASS_MISC, "esm@4100000", &dev);
		if (ret)
			printf("esm mcu init failed: %d\n", ret);

		enable_mcu_esm_reset();
	}

#if defined(CONFIG_K3_AM64_DDRSS)
	ret = uclass_get_device(UCLASS_RAM, 0, &dev);
	if (ret)
		panic("DRAM init failed: %d\n", ret);
#endif

	if (IS_ENABLED(CONFIG_SPL_ETH) && IS_ENABLED(CONFIG_TI_AM65_CPSW_NUSS) &&
	    spl_boot_device() == BOOT_DEVICE_ETHERNET) {
		struct udevice *cpswdev;

		if (uclass_get_device_by_driver(UCLASS_MISC, DM_DRIVER_GET(am65_cpsw_nuss),
						&cpswdev))
			printf("Failed to probe am65_cpsw_nuss driver\n");
	}

	spl_enable_dcache();

#ifndef CONFIG_SPL_MMC	
	writel(0x00050007,0x000F4110); /* GPIO0_67 */
	writel(0x00050007,0x000F419C); /* GPIO1_9 */
	writel(0x00050007,0x000F4210); /* GPIO1_38 */
	writel(0x00050007,0x000F40F4); /* GPIO0_60 */
	writel(0x00050007,0x000F40F8); /* GPIO0_61 */ 
	// writel(0x08,0x00600068);
	// writel(0xFFFFFFF7,0x00600060);
	am62x_gpio_set_direction(AM62X_GPIO0_BASE, POWER_GREEN_LED_GPIO, 0); /* GPIO0_67输出 */
	am62x_gpio_set_direction(AM62X_GPIO1_BASE, POWER_BACKLIGHT, 0); /* GPIO1_9输出 */
	am62x_gpio_set_direction(AM62X_GPIO1_BASE, POWER_KEYBOARD_LED, 0); /* GPIO1_38输出 */
	am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_GREEN_LED_GPIO, 1);
	am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_BACKLIGHT, 1);
	am62x_gpio_set_value(AM62X_GPIO1_BASE, POWER_KEYBOARD_LED, 1);

	am62x_gpio_set_direction(AM62X_GPIO0_BASE, POWER_BUTTON_GPIO, 1);  /* 输入 */
	am62x_gpio_set_direction(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 0);     /* 输出 */

	am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 1);
/*
位  | 掩码       | 名���                          | 描述                    | 启动类型
----|------------|-------------------------------|-------------------------|----------
 0  | 0x00000001 | MCU_RESET_PIN                 | MCU复位引脚        | Cold
 2  | 0x00000004 | MAIN_RESET_REQ                | Main域复位请求     | Neutral
 4  | 0x00000010 | THERMAL_RST                   | 热复位引脚               | Neutral
 8  | 0x00000100 | DEBUG_RST                     | 调试复位            | Neutral
12  | 0x00001000 | COLD_OUT_RST                  | 冷复位输出         | Cold
13  | 0x00002000 | WARM_OUT_RST                  | 热复位输出         | Warm
16  | 0x00010000 | SW_MCU_WARMRST                | 软件MCU热复位      | Warm
20  | 0x00100000 | SW_MAIN_WARMRST_FROM_MCU      | 从MCU的Main域软件热复位 | Warm
21  | 0x00200000 | SW_MAIN_WARMRST_FROM_MAIN     | 从Main域的软件热复位 | Warm
22  | 0x00400000 | DM_WDT_RST                    | DM看门狗复位       | Neutral
23  | 0x00800000 | DS_MAIN_PORZ                  | 深度睡眠Main域POR  | Cold
24  | 0x01000000 | SW_MAIN_POR_FROM_MCU          | 从MCU的Main域软件POR | Cold
25  | 0x02000000 | SW_MAIN_POR_FROM_MAIN         | 从Main域的软件POR  | Cold
30  | 0x40000000 | MAIN_ESM_ERROR                | Main域ESM错误        | Neutral
31  | 0x80000000 | MCU_ESM_ERROR                 | MCU域ESM错误         | Neutral
*/
	u32 rst_src_reg = readl(RESET_SRC_PHYSICAL_ADDR);
	
	if(rst_src_reg == 0x00000000 || rst_src_reg == 0x00000001 || rst_src_reg == 0x00001000) 
		check_power_button_spl();
#endif
}

u32 spl_mmc_boot_mode(struct mmc *mmc, const u32 boot_device)
{
	u32 devstat = readl(CTRLMMR_MAIN_DEVSTAT);
	u32 bootmode = (devstat & MAIN_DEVSTAT_PRIMARY_BOOTMODE_MASK) >>
				MAIN_DEVSTAT_PRIMARY_BOOTMODE_SHIFT;
	u32 bootmode_cfg = (devstat & MAIN_DEVSTAT_PRIMARY_BOOTMODE_CFG_MASK) >>
			    MAIN_DEVSTAT_PRIMARY_BOOTMODE_CFG_SHIFT;


	switch (bootmode) {
	case BOOT_DEVICE_EMMC:
		return MMCSD_MODE_EMMCBOOT;
	case BOOT_DEVICE_MMC:
		if (bootmode_cfg & MAIN_DEVSTAT_PRIMARY_MMC_FS_RAW_MASK)
			return MMCSD_MODE_RAW;
	default:
		return MMCSD_MODE_FS;
	}
}

static u32 __get_backup_bootmedia(u32 devstat)
{
	u32 bkup_bootmode = (devstat & MAIN_DEVSTAT_BACKUP_BOOTMODE_MASK) >>
				MAIN_DEVSTAT_BACKUP_BOOTMODE_SHIFT;
	u32 bkup_bootmode_cfg =
			(devstat & MAIN_DEVSTAT_BACKUP_BOOTMODE_CFG_MASK) >>
				MAIN_DEVSTAT_BACKUP_BOOTMODE_CFG_SHIFT;

	switch (bkup_bootmode) {
	case BACKUP_BOOT_DEVICE_UART:
		return BOOT_DEVICE_UART;

	case BACKUP_BOOT_DEVICE_USB:
		return BOOT_DEVICE_USB;

	case BACKUP_BOOT_DEVICE_ETHERNET:
		return BOOT_DEVICE_ETHERNET;

	case BACKUP_BOOT_DEVICE_MMC:
		if (bkup_bootmode_cfg)
			return BOOT_DEVICE_MMC2;
		return BOOT_DEVICE_MMC1;

	case BACKUP_BOOT_DEVICE_SPI:
		return BOOT_DEVICE_SPI;

	case BACKUP_BOOT_DEVICE_I2C:
		return BOOT_DEVICE_I2C;

	case BACKUP_BOOT_DEVICE_DFU:
		if (bkup_bootmode_cfg & MAIN_DEVSTAT_BACKUP_USB_MODE_MASK)
			return BOOT_DEVICE_USB;
		return BOOT_DEVICE_DFU;
	};

	return BOOT_DEVICE_RAM;
}

static u32 __get_primary_bootmedia(u32 devstat)
{
	u32 bootmode = (devstat & MAIN_DEVSTAT_PRIMARY_BOOTMODE_MASK) >>
				MAIN_DEVSTAT_PRIMARY_BOOTMODE_SHIFT;
	u32 bootmode_cfg = (devstat & MAIN_DEVSTAT_PRIMARY_BOOTMODE_CFG_MASK) >>
				MAIN_DEVSTAT_PRIMARY_BOOTMODE_CFG_SHIFT;

	switch (bootmode) {
	case BOOT_DEVICE_OSPI:
		fallthrough;
	case BOOT_DEVICE_QSPI:
		fallthrough;
	case BOOT_DEVICE_XSPI:
		fallthrough;
	case BOOT_DEVICE_SPI:
		return BOOT_DEVICE_SPI;

	case BOOT_DEVICE_ETHERNET_RGMII:
		fallthrough;
	case BOOT_DEVICE_ETHERNET_RMII:
		return BOOT_DEVICE_ETHERNET;

	case BOOT_DEVICE_EMMC:
		return BOOT_DEVICE_MMC1;

	case BOOT_DEVICE_SERIAL_NAND:
		return BOOT_DEVICE_SPINAND;

	case BOOT_DEVICE_NAND:
		return BOOT_DEVICE_NAND;

	case BOOT_DEVICE_MMC:
		if ((bootmode_cfg & MAIN_DEVSTAT_PRIMARY_MMC_PORT_MASK) >>
				MAIN_DEVSTAT_PRIMARY_MMC_PORT_SHIFT)
			return BOOT_DEVICE_MMC2;
		return BOOT_DEVICE_MMC1;

	case BOOT_DEVICE_DFU:
		if ((bootmode_cfg & MAIN_DEVSTAT_PRIMARY_USB_MODE_MASK) >>
		    MAIN_DEVSTAT_PRIMARY_USB_MODE_SHIFT)
			return BOOT_DEVICE_USB;
		return BOOT_DEVICE_DFU;

	case BOOT_DEVICE_NOBOOT:
		return BOOT_DEVICE_RAM;
	}

	return bootmode;
}

u32 spl_boot_device(void)
{
	u32 devstat = readl(CTRLMMR_MAIN_DEVSTAT);
	u32 bootmedia;

	if (bootindex == K3_PRIMARY_BOOTMODE)
		bootmedia = __get_primary_bootmedia(devstat);
	else
		bootmedia = __get_backup_bootmedia(devstat);

	debug("am625_init: %s: devstat = 0x%x bootmedia = 0x%x bootindex = %d\n",
	      __func__, devstat, bootmedia, bootindex);

	return bootmedia;
}

#include <command.h>

/**
 * U-Boot命令：设置热启动标记
 * 用法：warm_reboot
 * 在执行reboot命令前调用此命令，下次启动将跳过电源按钮检测
 */
static int do_warm_reboot(struct cmd_tbl *cmdtp, int flag, int argc, char *const argv[])
{
    printf("Setting warm boot marker...\n");
    am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 0);
    mdelay(500);
    am62x_gpio_set_value(AM62X_GPIO0_BASE, POWER_OFF_GPIO, 1);
    printf("Warm boot marker set. Next reboot will skip power button check.\n");
    printf("You can now execute 'reboot' command.\n");
    return 0;
}

U_BOOT_CMD(
    warm_reboot, 1, 1, do_warm_reboot,
    "Set warm boot marker for next reboot",
    "\n"
    "    - Set warm boot marker in RTC scratch register\n"
    "    - Next reboot will skip power button detection\n"
    "    - Use this before executing 'reboot' command\n"
);