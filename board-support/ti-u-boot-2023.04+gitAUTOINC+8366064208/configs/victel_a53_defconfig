CONFIG_ARM=y
CONFIG_ARCH_K3=y
CONFIG_TI_SECURE_DEVICE=y
CONFIG_SYS_MALLOC_F_LEN=0x8000
CONFIG_SPL_GPIO=y
CONFIG_SPL_LIBCOMMON_SUPPORT=y
CONFIG_SPL_LIBGENERIC_SUPPORT=y
CONFIG_NR_DRAM_BANKS=2
CONFIG_SOC_K3_AM625=y
CONFIG_K3_ATF_LOAD_ADDR=0x8e780000
CONFIG_TARGET_AM625_A53_EVM=y
CONFIG_HAS_CUSTOM_SYS_INIT_SP_ADDR=y
CONFIG_CUSTOM_SYS_INIT_SP_ADDR=0x80B80000
CONFIG_DM_GPIO=y
CONFIG_SPL_DM_SPI=y
CONFIG_DEFAULT_DEVICE_TREE="victel-am62"
CONFIG_SPL_TEXT_BASE=0x80080000
CONFIG_DM_RESET=y
# CONFIG_SPL_MMC is not set
CONFIG_SPL_SERIAL=y
CONFIG_SPL_DRIVERS_MISC=y
CONFIG_SPL_STACK_R_ADDR=0x82000000
CONFIG_SPL_SIZE_LIMIT=0x80000
CONFIG_SPL_SIZE_LIMIT_PROVIDE_STACK=0x800
CONFIG_SPL_FS_FAT=y
CONFIG_SPL_LIBDISK_SUPPORT=y
CONFIG_SPL_SPI_FLASH_SUPPORT=y
CONFIG_SPL_SPI=y
# CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
CONFIG_SPL_LOAD_FIT=y
CONFIG_SPL_LOAD_FIT_ADDRESS=0x81000000
CONFIG_DISTRO_DEFAULTS=y
CONFIG_BOOTCOMMAND="run bootak811;"
CONFIG_CMD_BOOTZ=y
CONFIG_BOARD_LATE_INIT=y
CONFIG_LED=y
CONFIG_LED_BLINK=y
CONFIG_SPL_LED=y
CONFIG_LED_GPIO=y
# CONFIG_LED_STATUS is not set
CONFIG_CMD_LED=y
# CONFIG_BLOBLIST is not set
# CONFIG_BLOBLIST_FIXED is not set
CONFIG_BLOBLIST_ADDR=0x80D00000
CONFIG_SPL_MAX_SIZE=0x80000
CONFIG_SPL_HAS_BSS_LINKER_SECTION=y
CONFIG_SPL_BSS_START_ADDR=0x80c80000
CONFIG_SPL_BSS_MAX_SIZE=0x80000
CONFIG_SPL_SYS_REPORT_STACK_F_USAGE=y
CONFIG_SPL_BOARD_INIT=y
CONFIG_SPL_SYS_MALLOC_SIMPLE=y
CONFIG_SPL_STACK_R=y
CONFIG_SYS_SPL_MALLOC=y
CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_SECTOR=y
CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_SECTOR=0x1400
CONFIG_SPL_DMA=y
# CONFIG_SPL_ENV_SUPPORT is not set
# CONFIG_SPL_ETH  is not set
# CONFIG_SPL_FS_LOAD_PAYLOAD_NAME is not set
# CONFIG_SPL_I2C  is not set
CONFIG_SPL_DM_MAILBOX=y
CONFIG_SPL_DM_SPI_FLASH=y
# CONFIG_SPL_NET is not set
# CONFIG_SPL_NET_VCI_STRING  is not set
CONFIG_SPL_POWER_DOMAIN=y
CONFIG_SPL_RAM_SUPPORT=y
CONFIG_SPL_RAM_DEVICE=y
# CONFIG_SPL_SPI_FLASH_TINY is not set
CONFIG_SPL_SPI_FLASH_SFDP_SUPPORT=y
CONFIG_SPL_SPI_LOAD=y
CONFIG_SYS_SPI_U_BOOT_OFFS=0x180000
# CONFIG_SPL_THERMAL  is not set
# CONFIG_SPL_USB_HOST  is not set
# CONFIG_SPL_USB_STORAGE  is not set
# CONFIG_SPL_USB_GADGET  is not set
# CONFIG_SPL_DFU is not set
CONFIG_SPL_YMODEM_SUPPORT=y
# CONFIG_SPL_BMP is not set
CONFIG_SYS_MAXARGS=64
CONFIG_CMD_CLK=y
# CONFIG_CMD_DFU is not set
CONFIG_CMD_GPIO=y
# CONFIG_CMD_GPT is not set
# CONFIG_CMD_I2C is not set
CONFIG_CMD_MMC=y
CONFIG_CMD_FAT=y
# CONFIG_FS_EXT4  is not set
# CONFIG_CMD_EXT4 is not set
# CONFIG_CMD_EXT2 is not set
CONFIG_CMD_REMOTEPROC=y
# CONFIG_CMD_UBIFS is not set
#CONFIG_CMD_USB is not set
#CONFIG_CMD_USB_MASS_STORAGE is not set
# CONFIG_CMD_BMP is not set
# CONFIG_CMD_TIME is not set
# CONFIG_CMD_EXT4_WRITE is not set
CONFIG_OF_CONTROL=y
CONFIG_SPL_OF_CONTROL=y
CONFIG_OF_SEPARATE=y
# CONFIG_MULTI_DTB_FIT is not set
# CONFIG_SPL_MULTI_DTB_FIT is not set
# CONFIG_SPL_MULTI_DTB_FIT_NO_COMPRESSION is not set
# CONFIG_NET_RANDOM_ETHADDR is not set
CONFIG_SPL_DM=y
CONFIG_SPL_DM_DEVICE_REMOVE=y
CONFIG_SPL_DM_SEQ_ALIAS=y
CONFIG_REGMAP=y
CONFIG_SPL_REGMAP=y
CONFIG_SYSCON=y
CONFIG_SPL_SYSCON=y
CONFIG_SPL_OF_TRANSLATE=y
CONFIG_CLK=y
CONFIG_SPL_CLK=y
CONFIG_CLK_TI_SCI=y
# CONFIG_DFU_MMC is not set
# CONFIG_DFU_RAM is not set
# CONFIG_DFU_SF is not set
# CONFIG_SYS_DFU_DATA_BUF_SIZE is not set
# CONFIG_SYS_DFU_MAX_FILE_SIZE is not set
CONFIG_DMA_CHANNELS=y
CONFIG_TI_K3_NAVSS_UDMA=y
CONFIG_TI_SCI_PROTOCOL=y
# CONFIG_SPL_DM_GPIO_LOOKUP_LABEL is not set
CONFIG_DA8XX_GPIO=y
# CONFIG_DM_PCA953X is not set
# CONFIG_SPL_DM_PCA953X is not set
CONFIG_DM_I2C=y
CONFIG_SYS_I2C_OMAP24XX=y
CONFIG_DM_MAILBOX=y
# CONFIG_DM_DEBUG is not set
# CONFIG_LOGLEVEL is not set
# CONFIG_SPL_LOGLEVEL is not set
CONFIG_K3_SEC_PROXY=y
# CONFIG_I2C_EEPROM is not set
# CONFIG_SPL_I2C_EEPROM is not set
CONFIG_FS_LOADER=y
# CONFIG_SUPPORT_EMMC_BOOT is not set
# CONFIG_MMC_IO_VOLTAGE is not set
# CONFIG_SPL_MMC_IO_VOLTAGE is not set
# CONFIG_MMC_UHS_SUPPORT=y
# CONFIG_SPL_MMC_UHS_SUPPORT is not set
# CONFIG_MMC_HS200_SUPPORT is not set
# CONFIG_SPL_MMC_HS200_SUPPORT is not set
CONFIG_MMC_SDHCI=y
CONFIG_MMC_SDHCI_ADMA=y
# CONFIG_SPL_MMC_SDHCI_ADMA is not set
CONFIG_MMC_SDHCI_AM654=y
CONFIG_MMC_BROKEN_CD=y
CONFIG_DM_SPI_FLASH=y
CONFIG_SF_DEFAULT_SPEED=25000000
CONFIG_SPI_FLASH_SFDP_SUPPORT=y
CONFIG_SPI_FLASH_SOFT_RESET=y
CONFIG_SPI_FLASH_SOFT_RESET_ON_BOOT=y
# CONFIG_SPI_FLASH_SPANSION is not set
# CONFIG_SPI_FLASH_S28HX_T is not set
# CONFIG_SPI_FLASH_MACRONIX=y
CONFIG_SPI_FLASH_GIGADEVICE=y
CONFIG_SPI_FLASH_ISSI=y
CONFIG_SPI_FLASH_WINBOND=y
# CONFIG_PHY_TI_DP83867 is not set
# CONFIG_PHY_FIXED is not set
# CONFIG_TI_AM65_CPSW_NUSS is not set
# CONFIG_PHY is not set
CONFIG_PINCTRL=y
CONFIG_SPL_PINCTRL=y
CONFIG_PINCTRL_SINGLE=y
CONFIG_CMD_PINMUX=y
CONFIG_CMD_DM=y
CONFIG_POWER_DOMAIN=y
CONFIG_TI_SCI_POWER_DOMAIN=y
CONFIG_DM_REGULATOR=y
CONFIG_SPL_DM_REGULATOR=y
CONFIG_DM_REGULATOR_FIXED=y
CONFIG_SPL_DM_REGULATOR_FIXED=y
CONFIG_DM_REGULATOR_GPIO=y
CONFIG_SPL_DM_REGULATOR_GPIO=y
CONFIG_K3_SYSTEM_CONTROLLER=y
CONFIG_REMOTEPROC_TI_K3_ARM64=y
CONFIG_REMOTEPROC_TI_K3_M4F=y
CONFIG_RESET_TI_SCI=y
CONFIG_DM_SERIAL=y
CONFIG_SOC_DEVICE=y
CONFIG_SOC_DEVICE_TI_K3=y
CONFIG_SOC_TI=y
CONFIG_SPI=y
CONFIG_DM_SPI=y
CONFIG_OMAP3_SPI=y
CONFIG_CADENCE_QSPI=y
CONFIG_CADENCE_QSPI_PHY=y
CONFIG_SYSRESET=y
CONFIG_SPL_SYSRESET=y
CONFIG_SYSRESET_TI_SCI=y
CONFIG_CMD_ECHO=y
# CONFIG_CMD_ITEST is not set
# CONFIG_CMD_SOURCE is not set
# CONFIG_CMD_SETEXPR is not set
CONFIG_CMD_LOADB=y
# CONFIG_CMD_LOADS is not set
# CONFIG_DM_THERMAL is not set
# CONFIG_USB is not set
# CONFIG_DM_USB_GADGET is not set
# CONFIG_SPL_DM_USB_GADGET is not set
# CONFIG_USB_XHCI_HCD is not set
# CONFIG_USB_DWC3 is not set
# CONFIG_USB_DWC3_GENERIC is not set
# CONFIG_SPL_USB_DWC3_GENERIC is not set
# CONFIG_SPL_USB_DWC3_AM62 is not set
# CONFIG_USB_DWC3_AM62 is not set
# CONFIG_USB_GADGET is not set
# CONFIG_USB_GADGET_MANUFACTURER is not set
# CONFIG_USB_GADGET_VENDOR_NUM is not set
# CONFIG_USB_GADGET_PRODUCT_NUM is not set
# CONFIG_USB_GADGET_DOWNLOAD is not set
CONFIG_VIDEO=y
# CONFIG_VIDEO_GC9307=y
CONFIG_SYS_WHITE_ON_BLACK=y
CONFIG_VIDEO_BPP16=y
CONFIG_SPLASH_SCREEN=y
CONFIG_SPLASH_SCREEN_ALIGN=y
CONFIG_VIDEO_LOGO=y
# CONFIG_VIDEO_TIDSS is not set
# CONFIG_SPL_VIDEO_TIDSS is not set
# CONFIG_VIDEO_BMP_GZIP is not set
# CONFIG_BMP_24BPP is not set
# CONFIG_BMP_32BPP is not set
CONFIG_SPL_VIDEO=y
CONFIG_SPL_VIDEO_LOGO=y
CONFIG_SPL_SPLASH_SCREEN=y
CONFIG_SPL_SYS_WHITE_ON_BLACK=y
CONFIG_SPL_SPLASH_SCREEN_ALIGN=y
CONFIG_SPL_BMP_16BPP=y
# CONFIG_SPL_SPLASH_SOURCE is not set
# CONFIG_SPL_VIDEO_BMP_GZIP is not set
# CONFIG_SPL_BMP_24BPP is not set
# CONFIG_SPL_BMP_32BPP is not set
# CONFIG_SPL_HIDE_LOGO_VERSION is not set
# CONFIG_FS_FAT_MAX_CLUSTSIZE is not set
# CONFIG_SPL_GZIP is not set

CONFIG_OF_LIBFDT=y
CONFIG_OF_LIBFDT_OVERLAY=y 

# CONFIG_GENERATE_SMBIOS_TABLE is not set
# CONFIG_EFI_LOADER is not set
# CONFIG_CMD_BOOTEFI_BOOTMGR is not set
# CONFIG_EFI_VARIABLE_FILE_STORE is not set
# CONFIG_EFI_VAR_BUF_SIZE is not set
# CONFIG_EFI_CAPSULE_MAX is not set
# CONFIG_EFI_DEVICE_PATH_TO_TEXT is not set
# CONFIG_EFI_DEVICE_PATH_UTIL is not set
# CONFIG_EFI_DT_FIXUP is not set
# CONFIG_EFI_LOADER_HII is not set
# CONFIG_EFI_UNICODE_COLLATION_PROTOCOL2 is not set
# CONFIG_EFI_UNICODE_CAPITALIZATION is not set
# CONFIG_EFI_PLATFORM_LANG_CODES is not set
# CONFIG_EFI_HAVE_RUNTIME_RESET is not set
# CONFIG_EFI_LOAD_FILE2_INITRD is not set
# CONFIG_EFI_ECPT is not set
# CONFIG_EFI_EBBR_2_1_CONFORMANCE is not set
# CONFIG_DOS_PARTITION is not set
# CONFIG_SPL_DOS_PARTITION is not set
# CONFIG_ISO_PARTITION is not set
# CONFIG_EFI_PARTITION is not set
# CONFIG_EFI_PARTITION_ENTRIES_NUMBERS is not set
# CONFIG_EFI_PARTITION_ENTRIES_OFF is not set
# CONFIG_SPL_EFI_PARTITION is not set
# CONFIG_ETH is not set
# CONFIG_LZ4 is not set
# CONFIG_LZMA is not set
# CONFIG_VPL_LZMA is not set
# CONFIG_BOOTMETH_GLOBAL is not set
# CONFIG_BOOTMETH_DISTRO is not set
# CONFIG_BOOTMETH_VBE is not set
# CONFIG_BOOTMETH_VBE_SIMPLE is not set
# CONFIG_BOOTMETH_VBE_SIMPLE_OS is not set
CONFIG_BOOTDELAY=1
# CONFIG_CMD_LZMADEC is not set
# CONFIG_CMD_UNLZ4 is not set
# CONFIG_CMD_PART is not set
# CONFIG_CMD_BOOTFLOW is not set 
# CONFIG_CMD_ELF is not set 
# CONFIG_SPI_FLASH_USE_4K_SECTORS is not set 
# CONFIG_TI_I2C_BOARD_DETECT is not set
CONFIG_BAUDRATE=1500000
# CONFIG_SPI_FLASH_UNLOCK_ALL is not set
CONFIG_LOCALVERSION_AUTO=y
CONFIG_LOCALVERSION="_V1.02.005_09020110"

CONFIG_LOG=y
CONFIG_LOGLEVEL=7
CONFIG_LOG_MAX_LEVEL=7
CONFIG_LOG_DEFAULT_LEVEL=7
CONFIG_SPL_LOG=y
CONFIG_DEBUG_SPI=y
CONFIG_DEBUG_GPIO=y
CONFIG_DM_DEBUG=y
CONFIG_CMD_LOG=y
CONFIG_LOG_CONSOLE=y